# serializers.py

from rest_framework import serializers
from django.contrib.auth import authenticate
from rest_framework_simplejwt.tokens import RefreshToken
from .models import StudioRequest, SchoolEnrollment, CustomUser

class StudioRequestSerializer(serializers.ModelSerializer):
    class Meta:
        model = StudioRequest
        fields = '__all__'

class SchoolEnrollmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SchoolEnrollment
        fields = '__all__'


class UserSerializer(serializers.ModelSerializer):
    """Сериализатор для данных пользователя"""
    class Meta:
        model = CustomUser
        fields = ('id', 'username', 'email', 'role', 'first_name', 'last_name')


class LoginSerializer(serializers.Serializer):
    """Сериализатор для входа в систему"""
    username = serializers.CharField()
    password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')

        if username and password:
            user = authenticate(username=username, password=password)
            if user:
                if user.is_active:
                    attrs['user'] = user
                    return attrs
                else:
                    raise serializers.ValidationError('Аккаунт пользователя отключен.')
            else:
                raise serializers.ValidationError('Неверные учетные данные.')
        else:
            raise serializers.ValidationError('Необходимо указать имя пользователя и пароль.')


class LoginResponseSerializer(serializers.Serializer):
    """Сериализатор для ответа при входе в систему"""
    refresh = serializers.CharField()
    access = serializers.CharField()
    user = UserSerializer()

    def create(self, validated_data):
        user = validated_data['user']
        refresh = RefreshToken.for_user(user)

        return {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'user': {
                'user_id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role,
            }
        }
