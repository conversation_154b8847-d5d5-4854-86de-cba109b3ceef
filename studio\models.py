from django.db import models

# Create your models here.
class StudioRequest(models.Model):
    STATUS_CHOICES = [
        ('new', 'Новая'),
        ('in_progress', 'В работе'),
        ('done', 'Завершена'),
    ]

    name = models.CharField(max_length=100, verbose_name="Имя")
    phone = models.CharField(max_length=20, verbose_name="Телефон")
    message = models.TextField(verbose_name="Запрос", blank=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Время заявки")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='new', verbose_name="Статус")

    def __str__(self):
        return f"{self.name} ({self.phone})"

    class Meta:
        verbose_name = "Заявка в студию"
        verbose_name_plural = "Заявки на разработку"
        ordering = ['-created_at']


class SchoolEnrollment(models.Model):
    name = models.CharField(max_length=100, verbose_name="Имя ребёнка")
    phone = models.CharField(max_length=20, verbose_name="Телефон родителя")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Дата Заявки")

    def __str__(self):
        return f"{self.name} ({self.phone})"

    class Meta:
        verbose_name = "Запись на курсы"
        verbose_name_plural = "Записи на курсы"
        ordering = ['-created_at']