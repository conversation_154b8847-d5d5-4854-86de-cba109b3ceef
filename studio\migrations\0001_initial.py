# Generated by Django 5.1.7 on 2025-06-21 12:40

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SchoolEnrollment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Имя ребёнка')),
                ('phone', models.CharField(max_length=20, verbose_name='Телефон родителя')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Дата Заявки')),
            ],
            options={
                'verbose_name': 'Запись на курсы',
                'verbose_name_plural': 'Записи на курсы',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StudioRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100, verbose_name='Имя')),
                ('phone', models.CharField(max_length=20, verbose_name='Телефон')),
                ('message', models.TextField(blank=True, verbose_name='Запрос')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Время заявки')),
                ('status', models.CharField(choices=[('new', 'Новая'), ('in_progress', 'В работе'), ('done', 'Завершена')], default='new', max_length=20, verbose_name='Статус')),
            ],
            options={
                'verbose_name': 'Заявка в студию',
                'verbose_name_plural': 'Заявки на разработку',
                'ordering': ['-created_at'],
            },
        ),
    ]
