# urls.py

from django.urls import path
from .views import (
    StudioRequestListView,
    StudioRequestDetailView,
    SchoolEnrollmentListView,
    SchoolEnrollmentDetailView
)

urlpatterns = [
    # Studio Request endpoints
    path('api/studio-requests/', StudioRequestListView.as_view(), name='studio-requests-list'),
    path('api/studio-requests/<int:pk>/', StudioRequestDetailView.as_view(), name='studio-requests-detail'),

    # School Enrollment endpoints
    path('api/school-enrollments/', SchoolEnrollmentListView.as_view(), name='school-enrollments-list'),
    path('api/school-enrollments/<int:pk>/', SchoolEnrollmentDetailView.as_view(), name='school-enrollments-detail'),
]
