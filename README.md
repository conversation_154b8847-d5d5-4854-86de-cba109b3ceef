# Base Django Project for dev. Setting<PERSON>, <PERSON>in<PERSON>, <PERSON><PERSON> etc.
# iteasy-studio-back
Complete API Endpoints
StudioRequest API:
GET /api/studio-requests/ - List all studio requests
POST /api/studio-requests/ - Create a new studio request
GET /api/studio-requests/<id>/ - Get a single studio request by ID (new)
PUT /api/studio-requests/<id>/ - Update a studio request completely (new)
PATCH /api/studio-requests/<id>/ - Update a studio request partially (new)
DELETE /api/studio-requests/<id>/ - Delete a studio request by ID
SchoolEnrollment API:
GET /api/school-enrollments/ - List all school enrollments
POST /api/school-enrollments/ - Create a new school enrollment
GET /api/school-enrollments/<id>/ - Get a single school enrollment by ID (new)
PUT /api/school-enrollments/<id>/ - Update a school enrollment completely (new)
PATCH /api/school-enrollments/<id>/ - Update a school enrollment partially (new)
DELETE /api/school-enrollments/<id>/ - Delete a school enrollment by ID